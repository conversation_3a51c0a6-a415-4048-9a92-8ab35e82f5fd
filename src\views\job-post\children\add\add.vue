<script setup lang="ts">
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';

import TitleBar from '@/components/common/title-bar.vue';
import WrapperForm from '@/components/common/wrapper-form.vue';
import WrapperFormTitle from '@/components/common/wrapper-form-title.vue';
import AddForm from '@/components/biz/job-post/apply-form.vue';


import { goBack } from '@/helps/navigation';
import { namespaceT } from '@/helps/namespace-t';
import { openToastError, openToastSuccess } from '@/helps/toast';
import { formScrollIntoError } from '@/utils/form-scroll-into-error';
// import { createProjectReportRules } from './hooks/rules';
import { AddApi } from '@/api/job-post/add';
import { DraftApi } from '@/api/job-post/draft';


const router = useRouter();
const t = namespaceT('jobPost');
const tc = namespaceT('common');
const loadingSubmit = ref(false);
const loadingDraft = ref(false);
const loadingContent = ref(false);
const model = reactive(() => {});


const formRef = ref();
// const rules = createProjectReportRules();

const onGoBack = () => {
  goBack(router);
};

const onDraft = async () => {
  try {
    loadingDraft.value = true;
    const api = new DraftApi({ id: 0 });
    api.data = {
      ...model,
    };
    await api.send();
    openToastSuccess(t('hint.draftSuccessfully'));
    onGoBack();
  } catch (error) {
    openToastError(error.message);
  } finally {
    loadingDraft.value = false;
  }
};

const onSubmit = async () => {
  try {
    loadingSubmit.value = true;
    const res = await formRef.value.validate();
    if (!res) {
      formScrollIntoError(formRef.value);
      return;
    }

    const api = new AddApi();
    api.data = {
      ...model,
    };

    await api.send();
    openToastSuccess(t('hint.savedSuccessfully'));
    onGoBack();
  } catch (error) {
    openToastError(error.message);
  } finally {
    loadingSubmit.value = false;
  }
};
</script>


<template>
  <div class="pima-form-page">
    <TitleBar
      go-back
      :title="t('form.add')"
      @go-back="onGoBack"
    />

    <WrapperForm :loading="loadingContent">
      <WrapperFormTitle :title="t('form.add')">
        <Form
          ref="formRef"
          class="pima-form"
          label-position="top"
          :model="model"
        >
          <!-- :rules="rules" -->
          <AddForm
            v-model="model"
          />
        </Form>
      </WrapperFormTitle>

      <template
        v-if="$can((P)=>P.JobPost.Add)"
        #action
      >
        <Button
          class="pima-btn mr-15"
          type="default"
          :disabled="loadingSubmit || loadingDraft"
          @click="onGoBack"
        >
          {{ tc('action.cancel') }}
        </Button>
        <Button
          class="pima-btn mr-15"
          type="default"
          :disabled="loadingDraft || loadingSubmit"
          @click="onDraft"
        >
          {{ t('action.draft') }}
        </Button>
        <Button
          class="pima-btn"
          type="primary"
          :loading="loadingSubmit"
          @click="onSubmit"
        >
          {{ t('action.apply') }}
        </Button>
      </template>
    </WrapperForm>
  </div>
</template>


<style lang="less" scoped>
:deep(.pima-wrapper-form-title){
  margin:30px auto;
}
</style>
