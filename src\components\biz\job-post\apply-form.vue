<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<script setup lang="ts">

import PimaInput from '@/components/common/pima-input.vue';
import SelectPostType from '@/components/biz/select/post-type.vue';

import { namespaceT } from '@/helps/namespace-t';


const model = defineModel<Record<string, any>>();

const t = namespaceT('jobPost');
const tc = namespaceT('common');


</script>


<template>
  <Row
    :gutter="24"
    class="pl-15"
  >
    <!-- 岗位名称 -->
    <Col :span="12">
      <FormItem
        prop="trainingBaseId"
        :label="t('label.postName')"
        class="no-colon"
      >
        <PimaInput
          v-model.trim="model.name"
        />
      </FormItem>
    </Col>

    <!-- 岗位类型 -->
    <Col :span="12">
      <FormItem
        prop="trainingBaseId"
        :label="t('label.postType')"
        :placeholder="tc('placeholder.select')"
        class="no-colon"
      >
        <SelectPostType
          v-model="model.name"
        />
      </FormItem>
    </Col>

      <!-- 研究方向 -->
    <Col :span="24">
      <FormItem
        prop="trainingBaseId"
        :label="t('label.researchDirection')"
        class="no-colon"
      >
        <PimaInput
          v-model.trim="model.name"
        />
      </FormItem>
    </Col>
  </Row>
</template>
